[{"directory": "/usr/games/Assignment2/build", "command": "/usr/bin/g++  -isystem /usr/local/include/opencv4 -g -std=gnu++17 -o CMakeFiles/Rasterizer.dir/main.cpp.o -c /usr/games/Assignment2/代码框架/main.cpp", "file": "/usr/games/Assignment2/代码框架/main.cpp", "output": "CMakeFiles/Rasterizer.dir/main.cpp.o"}, {"directory": "/usr/games/Assignment2/build", "command": "/usr/bin/g++  -isystem /usr/local/include/opencv4 -g -std=gnu++17 -o CMakeFiles/Rasterizer.dir/rasterizer.cpp.o -c /usr/games/Assignment2/代码框架/rasterizer.cpp", "file": "/usr/games/Assignment2/代码框架/rasterizer.cpp", "output": "CMakeFiles/Rasterizer.dir/rasterizer.cpp.o"}, {"directory": "/usr/games/Assignment2/build", "command": "/usr/bin/g++  -isystem /usr/local/include/opencv4 -g -std=gnu++17 -o CMakeFiles/Rasterizer.dir/Triangle.cpp.o -c /usr/games/Assignment2/代码框架/Triangle.cpp", "file": "/usr/games/Assignment2/代码框架/Triangle.cpp", "output": "CMakeFiles/Rasterizer.dir/Triangle.cpp.o"}]