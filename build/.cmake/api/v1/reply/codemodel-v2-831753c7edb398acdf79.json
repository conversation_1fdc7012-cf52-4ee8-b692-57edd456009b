{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "<PERSON>ster<PERSON>", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "Rasterizer::@6890427a1f51a3e7e1df", "jsonFile": "target-Rasterizer-Debug-3cc97a9e70795b41fd73.json", "name": "<PERSON>ster<PERSON>", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/usr/games/Assignment2/build", "source": "/usr/games/Assignment2/代码框架"}, "version": {"major": 2, "minor": 6}}