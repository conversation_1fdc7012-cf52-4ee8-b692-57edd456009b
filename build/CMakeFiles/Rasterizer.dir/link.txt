/usr/bin/g++ -g CMakeFiles/Rasterizer.dir/main.cpp.o CMakeFiles/Rasterizer.dir/rasterizer.cpp.o CMakeFiles/Rasterizer.dir/Triangle.cpp.o -o Rasterizer  -Wl,-rpath,/usr/local/lib /usr/local/lib/libopencv_gapi.so.4.12.0 /usr/local/lib/libopencv_highgui.so.4.12.0 /usr/local/lib/libopencv_ml.so.4.12.0 /usr/local/lib/libopencv_objdetect.so.4.12.0 /usr/local/lib/libopencv_photo.so.4.12.0 /usr/local/lib/libopencv_stitching.so.4.12.0 /usr/local/lib/libopencv_video.so.4.12.0 /usr/local/lib/libopencv_videoio.so.4.12.0 /usr/local/lib/libopencv_imgcodecs.so.4.12.0 /usr/local/lib/libopencv_dnn.so.4.12.0 /usr/local/lib/libopencv_calib3d.so.4.12.0 /usr/local/lib/libopencv_features2d.so.4.12.0 /usr/local/lib/libopencv_flann.so.4.12.0 /usr/local/lib/libopencv_imgproc.so.4.12.0 /usr/local/lib/libopencv_core.so.4.12.0 
