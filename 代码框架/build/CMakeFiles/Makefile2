# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /usr/games/Assignment2/代码框架

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /usr/games/Assignment2/代码框架/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/Rasterizer.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/Rasterizer.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/Rasterizer.dir

# All Build rule for target.
CMakeFiles/Rasterizer.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Rasterizer.dir/build.make CMakeFiles/Rasterizer.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Rasterizer.dir/build.make CMakeFiles/Rasterizer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/usr/games/Assignment2/代码框架/build/CMakeFiles --progress-num=1,2,3,4 "Built target Rasterizer"
.PHONY : CMakeFiles/Rasterizer.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/Rasterizer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /usr/games/Assignment2/代码框架/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/Rasterizer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /usr/games/Assignment2/代码框架/build/CMakeFiles 0
.PHONY : CMakeFiles/Rasterizer.dir/rule

# Convenience name for target.
Rasterizer: CMakeFiles/Rasterizer.dir/rule
.PHONY : Rasterizer

# clean rule for target.
CMakeFiles/Rasterizer.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Rasterizer.dir/build.make CMakeFiles/Rasterizer.dir/clean
.PHONY : CMakeFiles/Rasterizer.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

