# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/usr/games/Assignment2/代码框架/CMakeLists.txt"
  "CMakeFiles/3.28.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.28.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.28.3/CMakeSystem.cmake"
  "/usr/local/lib/cmake/opencv4/OpenCVConfig-version.cmake"
  "/usr/local/lib/cmake/opencv4/OpenCVConfig.cmake"
  "/usr/local/lib/cmake/opencv4/OpenCVModules-release.cmake"
  "/usr/local/lib/cmake/opencv4/OpenCVModules.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/Rasterizer.dir/DependInfo.cmake"
  )
